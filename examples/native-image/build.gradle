plugins {
    id "org.springframework.boot"
    id "org.graalvm.buildtools.native"
}

dependencies {
    implementation(project(":starters:httpexchange-spring-boot-starter"))

    testImplementation("org.springframework.boot:spring-boot-starter-test")
}

// https://graalvm.github.io/native-build-tools/latest/gradle-plugin.html#configuration-options
graalvmNative {
    testSupport = false
    binaries {
        main {
            verbose = true
            sharedLibrary = false
        }
    }
}
