plugins {
    id "org.springframework.boot"
}

dependencies {
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation(project(":starters:httpexchange-spring-boot-starter"))
    implementation("org.springframework.cloud:spring-cloud-starter-loadbalancer:${springCloudCommonsVersion}")

    implementation("org.springframework:spring-webflux")
    implementation("org.springframework.retry:spring-retry")

    testImplementation("org.springframework.boot:spring-boot-starter-test")
}
