name: Build

on:
  push:
    branches:
      - main
      - 3.4.x
      - 3.3.x
      - 3.2.x
      - 3.1.x
  pull_request:
    branches:
      - main
      - 3.4.x
      - 3.3.x
      - 3.2.x
      - 3.1.x

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Set up JDK
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'corretto'

      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build
        run: ./gradlew build

  native-image-build:
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - name: Check out the repo
        uses: actions/checkout@v4

      - name: Setup GraalVM
        uses: graalvm/setup-graalvm@v1
        with:
          java-version: '17'
          distribution: 'graalvm'
          github-token: ${{ secrets.GITHUB_TOKEN }}
          native-image-job-reports: 'true'

      - name: <PERSON>up Gradle
        uses: gradle/actions/setup-gradle@v4

      - name: Build native image
        run: |
          ./gradlew nativeRun
