pluginManagement {
    repositories {
        gradlePluginPortal()
        maven { url = "https://repo.spring.io/snapshot" }
        maven { url = "https://repo.spring.io/milestone" }
    }
}

rootProject.name = 'httpexchange-spring-boot-starter-root'

include(":examples:loadbalancer")
include(":examples:minimal")
include(":examples:native-image")
include(":examples:processor")
include(":examples:quick-start")
include(":examples:reactive")

include(":httpexchange-processor")
include(":httpexchange-spring-boot-autoconfigure")

include(":starters:httpexchange-restclient-spring-boot-starter")
include(":starters:httpexchange-spring-boot-starter")
include(":starters:httpexchange-webclient-spring-boot-starter")

new File("${rootDir}/.githooks").eachFile(groovy.io.FileType.FILES) {
    def f = new File("${rootDir}/.git/hooks")
    if (f.exists() && f.isDirectory()) {
        java.nio.file.Files.copy(it.toPath(), new File("${rootDir}/.git/hooks", it.name).toPath(), java.nio.file.StandardCopyOption.REPLACE_EXISTING)
    }
}