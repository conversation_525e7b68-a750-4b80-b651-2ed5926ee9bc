---
sidebar_position: 20
---


import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# LoadBalancer

Support to work with `spring-cloud-starter-loadbalancer` to achieve client side load balancing.

## Enable LoadBalancer

This feature is automatically enabled when the `spring-cloud-starter-loadbalancer` is present on the classpath.

<Tabs>
    <TabItem value="gradle" label="Gradle">
    ```groovy
    implementation("org.springframework.cloud:spring-cloud-starter-loadbalancer")
    ```
    </TabItem>
    <TabItem value="maven" label="Maven">
    ```xml
    <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-loadbalancer</artifactId>
    </dependency>
    ```
    </TabItem>
</Tabs>

## Disable LoadBalancer

Set `http-exchange.loadbalancer-enabled` to `false` to disable the load balancer for all HttpExchange clients.

```yaml title="application.yml"
http-exchange:
  loadbalancer-enabled: false
```

### Disable for Specific Channel

Disable the load balancer for a specific channel by setting `loadbalancer-enabled` to `false`.

```yaml title="application.yml"
http-exchange:
  channels:
    - base-url: user
      # highlight-next-line-as-added
      loadbalancer-enabled: false
      clients:
        - com.example.user.api.*Api
```

See [loadbalancer](https://github.com/DanielLiu1123/httpexchange-spring-boot-starter/tree/main/examples/loadbalancer) example.
