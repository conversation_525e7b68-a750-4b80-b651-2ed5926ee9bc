---
sidebar_position: 30
---

# Convert Bean to Query Parameter

In Spring Web/WebFlux (server side), it will automatically convert query parameters to Java Bean,
but `Spring Cloud OpenFeign` and `@HttpExchange` does not support to convert Java bean to query parameters by default.
In `Spring Cloud OpenFeign` you need `@SpringQueryMap` to achieve this feature.

`httpexhange-spring-boot-starter` supports this feature, and you don't need any additional annotations.

:::info
In order not to change the default behavior of Spring, this feature is disabled by default,
you can set `http-exchange.bean-to-query-enabled=true` to enable it.
:::

```java
public interface PostApi {
  @GetExchange("/posts")
  List<Post> findAll(Post condition);
}
```

Auto convert non-null *value type* fields of condition to query parameters. Such as primitive/wrapper types, String, etc.

If you don't want to enable this feature globally, you can use `@BeanParam`,
it is an equivalent replacement for [`@SpringQueryMap`](https://docs.spring.io/spring-cloud-openfeign/reference/spring-cloud-openfeign.html#feign-querymap-support).
For easier migration from Spring Cloud OpenFeign, `@SpringQueryMap` is also supported, though `@BeanParam` is recommended.

:::tip
The `@BeanParam` annotation's naming is inspired by [JAX-RS](https://docs.oracle.com/javaee%2F7%2Fapi%2F%2F/javax/ws/rs/BeanParam.html).
:::
