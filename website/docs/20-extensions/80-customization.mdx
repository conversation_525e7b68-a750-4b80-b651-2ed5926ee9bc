---
sidebar_position: 80
---

# Customization

This library is designed to be highly customizable. You can customize the behavior of the library by providing your own implementations.

## Custom [HttpServiceArgumentResolver](https://docs.spring.io/spring-framework/docs/current//javadoc-api/org/springframework/web/service/invoker/class-use/HttpServiceArgumentResolver.html)

```java
@Bean
HttpServiceArgumentResolver yourHttpServiceArgumentResolver() {
  return new YourHttpServiceArgumentResolver();
}
```

Auto-detect all the `HttpServiceArgumentResolver` beans, then apply them to build the `HttpServiceProxyFactory`.

## Change Client Type

There are two adapters for HttpExchange client: `RestClientAdapter` and `WebClientAdapter`.

```yaml title="application.yml"
http-exchange:
  client-type: REST_CLIENT
```

The framework will choose the appropriate adapter according to the http client interface.
If any method in the interface returns a reactive type (Mono/Flux), then `WebClient` will be used, otherwise `RestClient` will be used.
**In most cases, you don't need to explicitly specify the client type.**

:::warning
The `connectTimeout` settings are not supported by `WEB_CLIENT` when using version < `3.5.0`.
:::

## HttpClientCustomizer

Using [`HttpClientCustomizer`](https://github.com/DanielLiu1123/httpexchange-spring-boot-starter/blob/main/httpexchange-spring-boot-autoconfigure/src/main/java/io/github/danielliu1123/httpexchange/HttpClientCustomizer.java),
you can more freely customize the underlying Http client, such as setting up a proxy, setting up SSL, etc.

```java
// For RestClient
@Bean
HttpClientCustomizer.RestClientCustomizer restClientCustomizer() {
    return (restClientBuilder, channel) -> {
        if (Objects.equals(channel.getName(), "whichChannelYouWantToCustomize")) {
            var httpClient = HttpClient.newBuilder().build();
            restClientBuilder.requestFactory(new JdkClientHttpRequestFactory(httpClient));
        }
    };
}
```

```java
// For WebClient
@Bean
HttpClientCustomizer.WebClientCustomizer webClientCustomizer() {
    return (webClientBuilder, channel) -> {
        if (Objects.equals(channel.getName(), "whichChannelYouWantToCustomize")) {
            var httpClient = HttpClient.newBuilder().build();
            webClientBuilder.clientConnector(new JdkClientHttpConnector(httpClient));
        }
    };
}
```

## Deep Customization

If you're not happy with the autoconfigured Http client bean,
you can configure it using the ["original way"](https://docs.spring.io/spring-framework/reference/integration/rest-clients.html#rest-http-interface).

If you manually create the Http client bean, the autoconfigured Http client bean will not be created.

```java
interface RepositoryService {
    @GetExchange("/repos/{owner}/{repo}")
    Repository getRepository(@PathVariable String owner, @PathVariable String repo);
}

@Configuration
class RepositoryServiceConfiguration {
    @Bean
    public RepositoryService repositoryService(RestClient.Builder restClientBuilder) {
        RestClient restClient = RestClient.builder().baseUrl("https://api.github.com/").build();
        RestClientAdapter adapter = RestClientAdapter.create(restClient);
        HttpServiceProxyFactory factory = HttpServiceProxyFactory.builderFor(adapter).build();
        return factory.createClient(RepositoryService.class);
    }
}
```
