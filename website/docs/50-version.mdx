---
sidebar_position: 50
---

# Version

| Spring Boot | httpexchange-spring-boot-starter                                                                                                                                                                                                   |
|-------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 3.5.x       | [![Maven Central](https://img.shields.io/maven-central/v/io.github.danielliu1123/httpexchange-spring-boot-starter?versionPrefix=3.5.)](https://search.maven.org/artifact/io.github.danielliu1123/httpexchange-spring-boot-starter) |
| 3.4.x       | [![Maven Central](https://img.shields.io/maven-central/v/io.github.danielliu1123/httpexchange-spring-boot-starter?versionPrefix=3.4.)](https://search.maven.org/artifact/io.github.danielliu1123/httpexchange-spring-boot-starter) |
| 3.3.x       | [![Maven Central](https://img.shields.io/maven-central/v/io.github.danielliu1123/httpexchange-spring-boot-starter?versionPrefix=3.3.)](https://search.maven.org/artifact/io.github.danielliu1123/httpexchange-spring-boot-starter) |
| 3.2.x       | [![Maven Central](https://img.shields.io/maven-central/v/io.github.danielliu1123/httpexchange-spring-boot-starter?versionPrefix=3.2.)](https://search.maven.org/artifact/io.github.danielliu1123/httpexchange-spring-boot-starter) |                                                                                                                                                                                                                             |
| 3.1.x       | [![Maven Central](https://img.shields.io/maven-central/v/com.freemanan/httpexchange-spring-boot-starter?versionPrefix=3.1.)](https://search.maven.org/artifact/com.freemanan/httpexchange-spring-boot-starter)                     |                                                                                                                                                                                                                             |
| 3.0.x       | [![Maven Central](https://img.shields.io/maven-central/v/com.freemanan/httpexchange-spring-boot-starter?versionPrefix=3.0.)](https://search.maven.org/artifact/com.freemanan/httpexchange-spring-boot-starter)                     |                                                                                                                                                                                                                             |

:::tip
The version of `httpexchange-spring-boot-starter` is aligned with Spring Boot,
we check the latest version of Spring Boot daily and release the corresponding version.
:::

## Notable Releases

### [3.5.0](https://github.com/DanielLiu1123/httpexchange-spring-boot-starter/releases/tag/v3.5.0)

- **Backward Compatibility Deprecation**: Starting from version 3.5.0, backward compatibility with Spring Boot versions below 3.5.0 has been dropped. This decision was made due to extensive internal refactoring in Spring Boot 3.5.0.
- If you're using a Spring Boot version < 3.5.0, please continue using `httpexchange-spring-boot-starter` version 3.4.x.

### [3.2.0](https://github.com/DanielLiu1123/httpexchange-spring-boot-starter/releases/tag/v3.2.0)

- **Group ID Change**: The groupId has been updated from `com.freemanan` to `io.github.danielliu1123`.
