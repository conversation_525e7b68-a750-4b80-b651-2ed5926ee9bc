---
sidebar_position: 20
---


import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

# Generate Server Implementation

Generate default implementation for server, you can use the base implementation to implement the server side.

- Add Dependency

    <Tabs>
        <TabItem value="gradle" label="Gradle">
            ```groovy
            dependencies {
                annotationProcessor("io.github.danielliu1123:httpexchange-processor:latest")
            }
            compileJava {
                options.compilerArgs.add("-AhttpExchangeConfig=${projectDir}/httpexchange-processor.properties")
            }
            ```
        </TabItem>
        <TabItem value="maven" label="Maven">
            ```xml
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>io.github.danielliu1123</groupId>
                            <artifactId>httpexchange-processor</artifactId>
                            <version>latest</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-AhttpExchangeConfig=${project.basedir}/httpexchange-processor.properties</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            ```
        </TabItem>
    </Tabs>

- Generate Code

    <Tabs>
        <TabItem value="gradle" label="Gradle">
            ```shell
            ./gradlew clean compileJava
            ```
        </TabItem>
        <TabItem value="maven" label="Maven">
            ```shell
            ./mvnw clean compile
            ```
        </TabItem>
    </Tabs>

The processor will generate a base implementation class for each interface annotated with `@HttpExchange` or `@RequestMapping`.

There are two types of generated code, `ABSTRACT_CLASS` and `INTERFACE`,
generated class name is the interface name with suffix `Base` by default,
these can be configured in `httpexchange-processor.properties`.

Example:

<Tabs>
    <TabItem value="abstract class" label="ABSTRACT_CLASS">
        ```java
        // source code
        @HttpExchange("/user")
        public interface UserApi {
            @GetExchange("/{id}")
            UserDTO getUser(@PathVariable("id") String id);
        }

        // generated code
        public abstract class UserApiBase implements UserApi {
            @Override
            public UserDTO getUser(String id) {
                throw new ResponseStatusException(HttpStatus.NOT_IMPLEMENTED);
            }
        }

        // use generated code
        @RestController
        public class UserApiImpl extends UserApiBase {
            @Override
            public UserDTO getUser(String id) {
                return new UserDTO(id, "Foo");
            }
        }
        ```
    </TabItem>

    <TabItem value="interface" label="INTERFACE">
        ```java
        // source code
        @HttpExchange("/user")
        public interface UserApi {
            @GetExchange("/{id}")
            UserDTO getUser(@PathVariable("id") String id);
        }

        // generated code
        @HttpExchange("/user")
        public interface UserApiBase {
            @GetExchange("/{id}")
            default UserDTO getUser(@PathVariable("id") String id) {
                throw new ResponseStatusException(HttpStatus.NOT_IMPLEMENTED);
            }
        }

        // use generated code
        @RestController
        public class UserApiImpl implements UserApiBase {
            @Override
            public UserDTO getUser(String id) {
                return new UserDTO(id, "Foo");
            }
        }
        ```
    </TabItem>
</Tabs>

## Custom Configuration

You can create a `httpexchange-processor.properties` file in any directory and put configuration directives in it.
These apply to all source files in this directory and all child directories.

:::tip
Starting from version *******, please use the [`httpExchangeConfig`](https://github.com/DanielLiu1123/httpexchange-spring-boot-starter/blob/main/examples/quick-start/build.gradle#L16) option to specify the configuration file path,
it's friendly for incremental compilation.
:::

| Property         | Description                                                                                     | Default Value                              |
|------------------|-------------------------------------------------------------------------------------------------|--------------------------------------------|
| enabled          | Enable the processor                                                                            | true                                       |
| suffix           | Generated base implementation class name suffix                                                 | Base (if suffix and prefix are both empty) |
| prefix           | Generated base implementation class name prefix                                                 |                                            |
| generatedType    | Generated code type, support `ABSTRACT_CLASS` and `INTERFACE`                                   | ABSTRACT_CLASS                             |
| packages         | Packages to scan, use comma to separate multiple packages, support `Ant-style` pattern          | All packages                               |
| outputSubpackage | Generated base implementation class output subpackage, relative to the package of the interface |                                            |

For example:

```properties title="httpexchange-processor.properties"
enabled=true
suffix=Base
prefix=
generatedType=INTERFACE
packages=com.example.api
outputSubpackage=generated
```

:::info
The `httpexchange-processor.properties` file should be located in the project or module directory.<br/>
For Maven projects, it should be placed alongside the `pom.xml` file.<br/>
For Gradle projects, it should be placed alongside the `build.gradle` file.

When the version >= *******, please use the [`httpExchangeConfig`](https://github.com/DanielLiu1123/httpexchange-spring-boot-starter/blob/main/examples/quick-start/build.gradle#L16) option to specify the configuration file path.
You can place the configuration file anywhere.
:::
