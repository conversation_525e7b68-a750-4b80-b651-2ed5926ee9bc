---
sidebar_position: 40
---

# Configuration Properties

Configuration properties for the httpexchange-spring-boot-starter project.

This page was generated by [spring-configuration-property-documenter](https://github.com/rodnansol/spring-configuration-property-documenter/blob/master/docs/modules/ROOT/pages/gradle-plugin.adoc).

## Table of Contents
* [**httpexchange-spring-boot-autoconfigure**](#httpexchange-spring-boot-autoconfigure)
  * [**http-exchange** - `io.github.danielliu1123.httpexchange.HttpExchangeProperties`](#http-exchange)

  * [**http-exchange.refresh** - `io.github.danielliu1123.httpexchange.HttpExchangeProperties$Refresh`](#http-exchange.refresh)

## httpexchange-spring-boot-autoconfigure
### http-exchange
**Class:** `io.github.danielliu1123.httpexchange.HttpExchangeProperties`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| base-packages| java.util.Set&lt;java.lang.String&gt;| Base packages to scan, use \{@link EnableExchangeClients#basePackages} first if configured.| | | 
| base-url| java.lang.String| Default base url, &#x27;http&#x27; scheme can be omitted. &lt;p&gt; If loadbalancer is enabled, this value means the service id. &lt;ul&gt; &lt;li&gt; localhost:8080 &lt;/li&gt; &lt;li&gt; http://localhost:8080 &lt;/li&gt; &lt;li&gt; https://localhost:8080 &lt;/li&gt; &lt;li&gt; localhost:8080/api &lt;/li&gt; &lt;li&gt; user(service id) &lt;/li&gt; &lt;/ul&gt;| | | 
| bean-to-query-enabled| java.lang.Boolean| Whether to convert Java bean to query parameters, default value is \{@code false}.| false| | 
| channels| java.util.List&lt;io.github.danielliu1123.httpexchange.HttpExchangeProperties$Channel&gt;| Channels configuration.| | | 
| client-type| io.github.danielliu1123.httpexchange.HttpExchangeProperties$ClientType| Client Type, if not specified, an appropriate client type will be set. &lt;ul&gt; &lt;li&gt; Use \{@link ClientType#REST_CLIENT} if none of the methods in the client return Reactive type. &lt;li&gt; Use \{@link ClientType#WEB_CLIENT} if any method in the client returns Reactive type. &lt;/ul&gt; &lt;p&gt; In most cases, you don&#x27;t need to explicitly specify the client type. @see ClientType @since 3.2.0| | | 
| clients| java.util.Set&lt;java.lang.Class&lt;?&gt;&gt;| Exchange client interfaces to register as beans, use \{@link EnableExchangeClients#clients} first if configured. @since 3.2.0| | | 
| enabled| java.lang.Boolean| Whether to enable http exchange autoconfiguration, default \{@code true}.| true| | 
| headers| java.util.List&lt;io.github.danielliu1123.httpexchange.HttpExchangeProperties$Header&gt;| Default headers will be added to all the requests.| | | 
| http-client-reuse-enabled| java.lang.Boolean| Whether to enable http client reuse, default \{@code true}. &lt;p&gt; Same \{@link Channel} configuration will share the same http client if enabled. @since 3.2.2| true| | 
| loadbalancer-enabled| java.lang.Boolean| Whether to enable loadbalancer, default \{@code true}. &lt;p&gt; Prerequisites: &lt;ul&gt; &lt;li&gt; \{@code spring-cloud-starter-loadbalancer} dependency in the classpath.&lt;/li&gt; &lt;li&gt; \{@code spring.cloud.loadbalancer.enabled&#x3D;true}&lt;/li&gt; &lt;/ul&gt; @since 3.2.0| true| | 
| request-mapping-support-enabled| java.lang.Boolean| whether to process \{@link RequestMapping} based annotation, default \{@code false}. &lt;p color&#x3D;&quot;red&quot;&gt; Recommending to use \{@link HttpExchange} instead of \{@link RequestMapping}. @since 3.2.0| false| | 
| warn-unused-config-enabled| java.lang.Boolean| Whether to check unused configuration, default \{@code true}. @since 3.2.0| true| | 
### http-exchange.refresh
**Class:** `io.github.danielliu1123.httpexchange.HttpExchangeProperties$Refresh`

|Key|Type|Description|Default value|Deprecation|
|---|----|-----------|-------------|-----------|
| enabled| java.lang.Boolean| Whether to enable refresh exchange clients, default \{@code false}. &lt;p&gt; This feature needs \{@code spring-cloud-context} dependency in the classpath. &lt;p color&#x3D;&quot;orange&quot;&gt; NOTE: This feature is not supported by native image. @see &lt;a href&#x3D;&quot;https://github.com/spring-cloud/spring-cloud-release/wiki/AOT-transformations-and-native-image-support#refresh-scope&quot;&gt;Refresh Scope&lt;/a&gt;| false| | 

