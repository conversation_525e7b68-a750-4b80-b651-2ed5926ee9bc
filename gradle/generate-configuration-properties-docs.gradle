apply plugin: "org.rodnansol.spring-configuration-property-documenter"

// see https://github.com/rodnansol/spring-configuration-property-documenter/blob/master/docs/modules/ROOT/pages/gradle-plugin.adoc#multi-module-multiple-sub-projects
tasks.register('generateConfigurationPropertiesDocs') {
    dependsOn generateAndAggregateDocuments {
        documentName = "Configuration Properties"
        documentDescription = """
Configuration properties for the httpexchange-spring-boot-starter project.

This page was generated by [spring-configuration-property-documenter](https://github.com/rodnansol/spring-configuration-property-documenter/blob/master/docs/modules/ROOT/pages/gradle-plugin.adoc).
"""
        type = "MARKDOWN"

        metadataInputs {
            metadata {
                name = "httpexchange-spring-boot-autoconfigure"
                input = file("httpexchange-spring-boot-autoconfigure")
                excludedGroups = ["Unknown group"]
            }
        }

        outputFile = new File("build/configuration-properties.md")
    }
}
