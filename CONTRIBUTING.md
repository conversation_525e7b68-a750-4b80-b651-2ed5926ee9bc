# Contributing to HttpExchange Spring Boot Starter

Welcome to the httpexchange-spring-boot-starter project!
We are excited to have you onboard and contribute to this project,
which aims to simplify HTTP exchange in Spring Boot applications.
This project is released under the MIT license, and we welcome contributions of all forms.

## Steps for Contributing

1. **Submit an Issue**: Start by creating an issue in our GitHub repository. Your issue can be a feature request, a bug
   report, or any other valuable feedback.

2. **Fork the Repository**: Fork our repository on GitHub to start making your changes.

3. **Make Your Changes**: Work on the issue you've chosen. Don't forget to adhere to our coding standards and write
   tests as needed.

4. **Submit a Pull Request**: Once you're done with your changes, submit a pull request (PR) to merge your changes into
   the main branch.

5. **Code Review**: Your PR will be reviewed by our maintainers. Engage in the review process to discuss your
   contributions and make any necessary adjustments.

6. **Merge**: Once your PR is approved, it will be merged into the project!

## Code Style and Conventions

- **Code Formatting**: We use [palantir-java-format](https://github.com/palantir/palantir-java-format) as our code
  formatter.

  Run the following command to apply formatting:

    ```shell
    ./gradlew spotlessApply
    ```

- **Writing Tests**: Make sure to write tests for your code to ensure everything works as expected.
