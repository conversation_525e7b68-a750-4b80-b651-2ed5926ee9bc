package io.github.danielliu1123.httpexchange.it.normal;

import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * <AUTHOR>
 */
@HttpExchange("/api")
public interface Api {

    @GetExchange
    String get();

    @PostExchange
    default String post() {
        throw new UnsupportedOperationException();
    }
}
