package issues.issue73;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/12/1
 */
@Configuration(proxyBeanMethods = false)
@EnableAutoConfiguration
@Import(HttpClientConfiguration.class)
@RestController
@RequestMapping("/users")
class CfgWithHttpClientConfiguration {
    @GetMapping("/{id}")
    public String getUsername(@PathVariable("id") String id) {
        return "Hello " + id;
    }
}
