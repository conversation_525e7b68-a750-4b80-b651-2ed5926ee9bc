spring:
  http:
    client:
      read-timeout: 10s
      connect-timeout: 1s
      ssl:
        bundle: bundle1
http-exchange:
  base-packages: [ com.example.api ]
  base-url: http://api-gateway
  bean-to-query-enabled: false
  request-mapping-support-enabled: false
  headers:
    - key: X-App-Name
      values: ${spring.application.name}
  refresh:
    enabled: true
  client-type: rest_client
  warn-unused-config-enabled: true
  loadbalancer-enabled: true
  channels:
    - base-url: http://order
      ssl:
        bundle: bundle2
      headers:
        - key: X-Key
          values: [ value1, value2 ]
      clients:
        - com.example.api.OrderItemApi
        - com.**.*Api
        - com.example.**
    - base-url: user
      classes:
        - com.example.api.UserApi
        - com.example.api.UserDetailApi
      client-type: rest_client
      connect-timeout: 1000
      read-timeout: 5000
