buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath "org.rodnansol:spring-configuration-property-documenter-gradle-plugin:${springConfigurationPropertyDocumenterVersion}"
    }
}

plugins {
    id "com.diffplug.spotless" version "${spotlessVersion}" apply false
    id "com.github.spotbugs" version "${spotbugsVersion}" apply false
    id "io.spring.dependency-management" version "${springDependencyManagementVersion}" apply false
    id "org.springframework.boot" version "${springBootVersion}" apply false
    id "org.graalvm.buildtools.native" version "${graalvmBuildToolsVersion}" apply false
    id "org.jreleaser" version "${jReleaserVersion}" apply false
    id "io.spring.nullability" version "${nullabilityVersion}" apply false
}

allprojects {

    apply plugin: "java"
    apply plugin: "java-library"

    repositories {
        mavenCentral()
        maven { url = "https://repo.spring.io/snapshot" }
        maven { url = "https://repo.spring.io/milestone" }
    }

    sourceSets {
        optionalSupport
    }

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(17)
        }
        registerFeature("optionalSupport") {
            usingSourceSet(sourceSets.optionalSupport)
        }
    }

    javadoc {
        // Suppress warnings about missing Javadoc comments
        options.addStringOption('Xdoclint:none', '-quiet')
    }

    apply plugin: "io.spring.dependency-management"
    dependencyManagement {
        imports {
            mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
        }
    }
    dependencies {
        compileOnly("org.projectlombok:lombok")
        annotationProcessor("org.projectlombok:lombok")
        testCompileOnly("org.projectlombok:lombok")
        testAnnotationProcessor("org.projectlombok:lombok")
    }

    compileJava {
        options.encoding = "UTF-8"
        options.compilerArgs << "-parameters"
    }

    compileTestJava {
        options.encoding = "UTF-8"
        options.compilerArgs << "-parameters"
    }

    test {
        systemProperties("spring.cloud.compatibility-verifier.enabled": "false")

        useJUnitPlatform()

        dependencies {
            // https://docs.gradle.org/current/userguide/upgrading_version_8.html#test_framework_implementation_dependencies
            testRuntimeOnly("org.junit.platform:junit-platform-launcher")
        }
    }

    afterEvaluate {
        tasks.findByName("bootRun")?.configure {
            systemProperty("spring.cloud.compatibility-verifier.enabled", "false")
        }
    }

    apply plugin: "com.diffplug.spotless"
    spotless {
        encoding "UTF-8"
        java {
            toggleOffOn()
            removeUnusedImports()
            trimTrailingWhitespace()
            endWithNewline()
            palantirJavaFormat()

            targetExclude("build/generated/**")

            custom("Refuse wildcard imports", {
                if (it =~ /\nimport .*\*;/) {
                    throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
                }
            } as Closure<String>)
        }
    }

    apply plugin: "com.github.spotbugs"
    spotbugs {
        spotbugsTest.enabled = false
        omitVisitors.addAll("FindReturnRef", "DontReusePublicIdentifiers")
    }

    apply plugin: "io.spring.nullability"
}

apply from: "${rootDir}/gradle/generate-configuration-properties-docs.gradle"
apply from: "${rootDir}/gradle/jreleaser.gradle"
